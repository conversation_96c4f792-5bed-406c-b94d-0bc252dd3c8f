package com.datatech.slgzt.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 谐云配置属性测试类
 * 验证XieyunProperties配置类是否正常工作
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@SpringBootTest
@TestPropertySource(properties = {
    "xieyun.adminAccount=testAdmin",
    "xieyun.adminPassword=testPassword",
    "xieyun.xieyunUrl=http://test.xieyun.com",
    "xieyun.publicKey=testPublicKey",
    "xieyun.clusterName=testCluster"
})
public class XieyunPropertiesTest {

    @Resource
    private XieyunProperties xieyunProperties;

    @Test
    public void testXieyunPropertiesLoaded() {
        // 验证配置属性是否正确加载
        assertNotNull(xieyunProperties, "XieyunProperties应该被正确注入");
        assertEquals("testAdmin", xieyunProperties.getAdminAccount(), "管理员账号应该正确加载");
        assertEquals("testPassword", xieyunProperties.getAdminPassword(), "管理员密码应该正确加载");
        assertEquals("http://test.xieyun.com", xieyunProperties.getXieyunUrl(), "谐云URL应该正确加载");
        assertEquals("testPublicKey", xieyunProperties.getPublicKey(), "公钥应该正确加载");
        assertEquals("testCluster", xieyunProperties.getClusterName(), "集群名称应该正确加载");
    }
}
