package com.datatech.slgzt.impl.manager.xieyun;

import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunRepoManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunRepositoryLocalManager;
import com.datatech.slgzt.model.dto.XieYunRepositoryDTO;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.HashMap;

@Service
public class XieyunRepoManagerImpl implements XieyunRepoManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunRepositoryLocalManager localManager;

    @Resource
    private XieYunOpmManagerConvert opmManagerConvert;

    /**
     * 创建仓库
     *
     * @param registryId 这个目前看是个配置项，让上传固定传输把
     * @param name
     * @return
     */
    @Override
    @Transactional
    public String createRepo(String registryId, String name) {
        //创建远程仓库
        String uriString = UriComponentsBuilder.fromPath("/caas-registry/registries/{registryId}/repoProjects")
                .buildAndExpand(registryId).toUriString();
        //创建远程数据
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("name", name);
        param.put("pvt", true);
        param.put("virtual", false);
        param.put("storageLimit", "-1");
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(param)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .post()
                .getBody()
                .toMapper();
        String repoId = XieyunUnpackUtil.unpackData(mapper, "调用底层创建仓库接口失败");

        // 本地创建仓库
        XieYunRepositoryDTO repositoryDTO = new XieYunRepositoryDTO()
                .setXieYunRepoId(repoId)
                .setXieYunRegistryId(registryId)
                .setRepoName(name);
        localManager.insert(repositoryDTO);

        return repoId;
    }

    /**
     * 删除仓库
     */
    @Override
    public String deleteRepo(String registryId, String repoId) {
        //创建远程仓库
        String uriString = UriComponentsBuilder.fromPath("/caas-registry/registries/{registryId}/repoProjects/{repoId}")
                .buildAndExpand(registryId, repoId).toUriString();
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .delete()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "调用底层删除仓库接口失败");
    }


    /**
     * 分配组织
     *
     * @param registryId
     * @param orgId
     * @param repoId
     */
    @Override
    public String assignOrg(String registryId, String orgId, String repoId) {
        XieYunRepositoryDTO repositoryDTO = new XieYunRepositoryDTO()
                .setXieYunRegistryId(registryId)
                .setXieYunRepoId(repoId)
                .setXieYunOrgId(orgId);
        localManager.updateByRepoId(repositoryDTO);

        //创建远程仓库
        String uriString = UriComponentsBuilder.fromPath("/caas-registry/registries/{registryId}/repoProjects/{repoId}/organizations")
                .buildAndExpand(registryId, repoId).toUriString();
        //创建远程数据
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("organIdList", Lists.newArrayList(orgId));
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(param)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .post()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "调用底层分配组织接口失败");
    }

    /**
     * 删除仓库分配组织
     */
    @Override
    public String deleteAssignOrg(String registryId, String repoId) {
        //创建远程仓库
        String uriString = UriComponentsBuilder.fromPath("/caas-registry/registries/{registryId}/repoProjects/{repoId}/organizations")
                .buildAndExpand(registryId, repoId).toUriString();
        //创建远程数据
        HashMap<String, Object> param = Maps.newHashMap();
        //远程删除是把组织ID传空数组
        param.put("organIdList", Lists.newArrayList());
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(param)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .post()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "调用底层分配组织接口失败");
    }
}
