package com.datatech.slgzt.impl.service.dag;

import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DagCreateJobListener implements JobExecutionListener {

    @Override
    public void beforeJob(JobExecution jobExecution) {
        // 可以在这里添加一些初始化操作，比如设置默认参数等
        System.out.println("Job即将执行: " + jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            JobParameters jobParameters = jobExecution.getJobParameters();
            System.out.println("Job执行失败: " + jobParameters.getLong("subOrderId"));
            // 获取失败异常
            List<Throwable> exceptions = jobExecution.getAllFailureExceptions();
            String errorMsg = exceptions.stream()
                .map(Throwable::getMessage)
                .collect(Collectors.joining(", "));
            // 可以发送告警邮件/短信等
            System.err.println("Job执行失败: " + errorMsg);
        }
    }
}