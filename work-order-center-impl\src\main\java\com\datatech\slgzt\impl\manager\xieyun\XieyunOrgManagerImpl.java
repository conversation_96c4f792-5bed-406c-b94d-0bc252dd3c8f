package com.datatech.slgzt.impl.manager.xieyun;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunOrgManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunOrgLocalManager;
import com.datatech.slgzt.model.dto.XieYunOrgDTO;
import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.query.container.XieYunOrgQuery;
import com.datatech.slgzt.model.xieyun.XieyunOrgCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunOrgQuotaOpm;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collection;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 15:33:39
 */
@Service
@Slf4j
public class XieyunOrgManagerImpl implements XieyunOrgManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunOrgLocalManager orgLocalManager;

    @Resource
    private XieYunOpmManagerConvert opmManagerConvert;

    /**
     * 创建组织
     *
     * @param opm
     */
    @Override
    @Transactional
    public String createOrg(XieyunOrgCreateOpm opm) {
        //创建远程组织
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/caas-amp/organizations")
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .addHeader("Amp-App-Id", "210482")
                .post()
                .getBody()
                .toMapper();
        String orgId = XieyunUnpackUtil.unpackData(mapper, "调用底层创建组织接口失败");

        //创建本地组织
        XieYunOrgDTO xieYunOrgDTO = opmManagerConvert.orgOpm2dto(opm);
        xieYunOrgDTO.setXieYunOrgId(orgId);
        orgLocalManager.insert(xieYunOrgDTO);
        return orgId;
    }

    /**
     * 查询组织
     *
     * @param orgId
     */
    @Override
    public String queryOrg(String orgName) {
        XieYunOrgQuery query = new XieYunOrgQuery().setName(orgName);
        List<XieYunOrgDTO> list = orgLocalManager.list(query);
        if (!CollectionUtils.isEmpty(list)) {
            log.info("谐云组织：{} 在数据库中已存在，直接返回组织id：{}", orgName, list.get(0).getXieYunOrgId());
            return list.get(0).getXieYunOrgId();
        }
        return null;
    }

    /**
     * 分配额度
     */
    @Override
    @Transactional
    public String orgQuota(String clusterName, String nodePoolName, String orgId, XieyunOrgQuotaOpm opm) {
        String uriString = UriComponentsBuilder.fromPath("/olympus-core/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota")
                .buildAndExpand(clusterName, nodePoolName, orgId).toUriString();
        //创建远程数据
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .post()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层分配额度接口失败");

        // 给组织分配资源
        XieYunOrgDTO orgDTO = opmManagerConvert.orgQuotaOpm2dto(opm);
        orgDTO.setXieYunOrgId(orgId)
                .setClusterName(clusterName)
                .setNodePoolName(nodePoolName);
        orgLocalManager.updateOrgQuotaByOrgId(orgDTO);
        return data;
    }

    @Override
    public QuotaResultDTO selectOrgQuota(String clusterName, String nodePoolName, String orgId) {
        // /olympus-core/clusters/eic-wz-cluster/nodepools/default/organizations/3849406042732158976/quota?clusterName=eic-wz-cluster&nodePoolName=default&organizationId=3849406042732158976
        String uriString = UriComponentsBuilder
                .fromPath("/olympus-core/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{orgId}")
                .buildAndExpand(clusterName, nodePoolName, orgId).toUriString();
        // 创建远程数据
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", "1")
                .get()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层分配额度接口失败");

        return JSON.parseObject(data, QuotaResultDTO.class);
    }
}
