package com.datatech.slgzt.impl.manager.xieyun;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunNamespacesManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunNamespaceLocalManager;
import com.datatech.slgzt.model.dto.XieYunNamespaceDTO;
import com.datatech.slgzt.model.xieyun.XieyunNamespaceCreateOpm;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 17:56:40
 */
@Service
public class XieyunNamespacesManagerImpl implements XieyunNamespacesManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunNamespaceLocalManager localManager;

    @Resource
    protected XieYunOpmManagerConvert opmManagerConvert;

    @Override
    @Transactional
    public String createNamespace(String orgId, String projectId, String clusterName, XieyunNamespaceCreateOpm opm) {
        //远程创建命名空间
        String uriString = UriComponentsBuilder.fromPath("/olympus-core/organizations/{orgId}/projects/{projectId}/clusters/{clusterName}/namespaces")
                .buildAndExpand(orgId, projectId, clusterName).toUriString();
        //创建远程组织
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", "210482")
                .post()
                .getBody()
                .toMapper();
        String namespaceId = XieyunUnpackUtil.unpackData(mapper, "调用底层创建命名空间接口失败");

        //本地创建命名空间
        XieYunNamespaceDTO namespaceDTO = opmManagerConvert.namespaceOpm2dto(opm);
        namespaceDTO.setXieYunNamespaceId(namespaceId)
                .setXieYunOrgId(orgId)
                .setXieYunProjectId(projectId);

        localManager.insert(namespaceDTO);
        return namespaceId;
    }


    @Override
    public String configNamespaceNetwork(String namespaceId, String orgId, String projectId, String clusterName, List<String> namespaceList) {
        // 创建远程
        String ipPool = "default-ippool";
        XieYunNamespaceDTO namespaceDTO = new XieYunNamespaceDTO();
        namespaceDTO.setIpPool(ipPool)
                .setXieYunNamespaceId(namespaceId);
        localManager.updateByNamespaceId(namespaceDTO);

        //远程创建命名空间
        String uriString = UriComponentsBuilder.fromPath("/olympus-core/organizations/{orgId}/projects/{projectId}/clusters/{clusterName}/ipPoolTypes/random/networkIpPools/{ipPool}/namespaces")
                .buildAndExpand(orgId, projectId, clusterName).toUriString();
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .setBodyPara(JSON.toJSONString(namespaceList))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", "210482")
                .put()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "调用底层创建命名空间接口失败");
    }
}
