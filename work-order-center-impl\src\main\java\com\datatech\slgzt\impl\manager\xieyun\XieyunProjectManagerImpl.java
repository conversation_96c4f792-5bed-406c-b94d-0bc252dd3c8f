package com.datatech.slgzt.impl.manager.xieyun;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunProjectManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunProjectLocalManager;
import com.datatech.slgzt.model.dto.XieYunProjectDTO;
import com.datatech.slgzt.model.xieyun.XieyunProjectCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunProjectQuotaOpm;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 16:25:46
 */
@Service
public class XieyunProjectManagerImpl implements XieyunProjectManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunProjectLocalManager projectLocalManager;

    @Resource
    private XieYunOpmManagerConvert opmManagerConvert;

    @Override
    @Transactional
    public String createProject(String orgId, XieyunProjectCreateOpm opm) {
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/caas-amp/organization/" + orgId + "/projects")
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", "210482")
                .post()
                .getBody()
                .toMapper();
        String projectId = XieyunUnpackUtil.unpackData(mapper, "创建项目调用底层接口失败");

        // 创建本地项目数据
        XieYunProjectDTO projectDTO = opmManagerConvert.projectOpm2dto(opm);
        projectDTO.setXieYunOrgId(orgId)
                .setXieYunProjectId(projectId);
        projectLocalManager.insert(projectDTO);
        return projectId;
    }

    /**
     * 项目分配额度
     */
    @Override
    @Transactional
    public String projectQuota(String orgId, String projectId, String cluster, String nodepool, XieyunProjectQuotaOpm opm) {
        //先本地创建
        XieYunProjectDTO projectDTO = opmManagerConvert.projectQuotaOpm2dto(opm);
        projectDTO.setClusterName(cluster)
                .setXieYunProjectId(projectId)
                .setXieYunOrgId(orgId)
                .setNodePoolName(nodepool)
                .setUpdatedTime(LocalDateTime.now());
        projectLocalManager.updateByProjectId(projectDTO);

        //如果创建成功，调用xieyun接口创建
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/olympus-core/organizations/" + orgId + "/clusters/"+cluster+"/nodepools/"+nodepool+"/projects/" + projectId + "/quota")
               .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", "210482")
                .post()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "项目分配额度调用底层接口失败");
    }

}
