package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 谐云配置属性类
 * 统一管理谐云相关的配置项
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "xieyun")
public class XieyunProperties {

    /**
     * 管理员账号
     */
    private String adminAccount;

    /**
     * 管理员密码
     */
    private String adminPassword;

    /**
     * 谐云服务URL
     */
    private String xieyunUrl;

    /**
     * RSA公钥
     */
    private String publicKey;

    /**
     * 集群名称
     */
    private String clusterName;
}
