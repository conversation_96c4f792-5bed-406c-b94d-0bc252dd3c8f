package com.datatech.slgzt.helps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.RsaUtil;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Maps;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 14:46:04
 */
@Component
public class Xie<PERSON>unLoginHelper implements InitializingBean {

    public static XieYunLoginHelper INSTANCE;

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private RedissonClient redissonClient;



    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    public String login(String username, String password) {
        //登录参数
        Map<String,String> requestMap = Maps.newHashMap();
        //密码需要rsa加密
        String encryptPasswork = RsaUtil.encrypt(password, xieyunProperties.getPublicKey());
        requestMap.put("username", username);
        requestMap.put("password", encryptPasswork);
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/caas-amp/auth/login")
                .addBodyPara(requestMap)
                .addHeader("Amp-Organ-Id", "1")
                .bodyType("json")
                .post()
                .getBody()
                .toMapper();
        //获取token
        String jsonStr = XieyunUnpackUtil.unpackData(mapper, "调用底层登录接口失败");
        //转成JSONOBJ
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        return jsonObject.getString("token");

    }

    /**
     * 获取adminToken
     */
    public String getAdminToken() {
        RMapCache<String, String> xieyunUser = redissonClient.getMapCache("xieyun_user");
        String token = xieyunUser.get("admin_token");
        if (ObjNullUtils.isNotNull(token)) {
            return token;
        }
        //登录
        token = login(xieyunProperties.getAdminAccount(), xieyunProperties.getAdminPassword());
        if (ObjNullUtils.isNotNull(token)) {
            //缓存30分钟
            xieyunUser.put("admin_token", token, 60 * 30, TimeUnit.SECONDS);
            return token;
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        INSTANCE = this;
    }
}
